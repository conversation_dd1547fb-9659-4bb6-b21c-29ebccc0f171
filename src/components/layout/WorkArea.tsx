'use client'

import React, { useState } from 'react'
import { useAppStore, useActiveTab, useActiveStreamingNote } from '@/lib/store'
import LightBrowser from '@/components/ui/LightBrowser'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import { WelcomeScreen } from '@/components/WelcomeScreen'
// import { ContentViewer } from '@/components/ContentViewer'
import EditableNotesViewer from '@/components/ui/EditableNotesViewer'
import BentoN<PERSON>Viewer from '@/components/ui/BentoNotesViewer'
import CardFlowLayout from '@/components/ui/CardFlowLayout'
import PureContentViewer from '@/components/ui/PureContentViewer'
// import SimpleDiffViewer from '@/components/ui/SimpleDiffViewer'

const WorkArea: React.FC = () => {
  const {
    tabs,
    addTab,
    setProcessing,
    activeMiddleTab,
    originalPanelWidth,
    setOriginalPanelWidth,
    isPureModeEnabled
  } = useAppStore()
  const activeTab = useActiveTab()
  const streamingNote = useActiveStreamingNote()
  const [inputValue, setInputValue] = useState('')
  const [isDraggingOriginalSplitter, setIsDraggingOriginalSplitter] = useState(false)

  // 处理输入提交 - 暂时使用旧系统，避免客户端导入问题
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim()) return

    setProcessing(true)

    try {
      // 判断输入类型并规范化URL
      let processedInput = inputValue.trim()
      let isUrl = false

      // 检测和规范化URL
      if (processedInput.startsWith('http://') || processedInput.startsWith('https://')) {
        isUrl = true
      } else if (processedInput.includes('.') && !processedInput.includes(' ') && processedInput.length > 3) {
        // 可能是没有协议的URL，自动添加https://
        processedInput = 'https://' + processedInput
        isUrl = true
      }

      if (isUrl) {
        // 对于URL：立即创建标签页并显示浏览器，在browse模式下不进行AI分析
        const tabId = addTab({
          title: new URL(processedInput).hostname,
          sourceType: 'url',
          sourceData: processedInput,
          originalContent: '',
          aiNoteMarkdown: '',
          isLoading: false,
          aiAnalyzing: false // browse模式下禁用AI分析
        })

        setInputValue('')
        setProcessing(false)

        // 在browse模式下不调用AI分析，保留接口以备后续使用
        // processUrlInBackground(processedInput, tabId)
      } else {
        // 对于文本：在browse模式下不进行AI分析
        const tabId = addTab({
          title: '文本内容',
          sourceType: 'text',
          sourceData: inputValue,
          originalContent: inputValue, // 直接设置原文内容
          aiNoteMarkdown: '',
          isLoading: false,
          aiAnalyzing: false // browse模式下禁用AI分析
        })

        setInputValue('')
        setProcessing(false)

        // 在browse模式下不调用AI分析，保留接口以备后续使用
        // processContentWithStream(processedInput, 'text', tabId)
      }
    } catch (error) {
      console.error('处理错误:', error)
      setProcessing(false)
      // 可以在这里显示错误提示
    }
  }

  // 流式处理内容分析（支持URL和文本）- 保持向后兼容
  const processContentWithStream = async (input: string, type: 'url' | 'text', tabId: string) => {
    try {
      console.log('开始流式AI分析:', input)

      const response = await fetch('/api/process/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input,
          type
        }),
        signal: AbortSignal.timeout(60000) // 60秒超时，给AI生成更多时间
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      let accumulatedAINote = ''
      const decoder = new TextDecoder()
      let basicInfoReceived = false
      let lastUpdateTime = 0
      let pendingUpdate = false
      const UPDATE_THROTTLE = 50 // 优化更新频率为50ms，提升响应性

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              // 完成流式更新
              const finalTab = useAppStore.getState().tabs.find(t => t.id === tabId)
              useAppStore.getState().updateTab(tabId, {
                aiNoteMarkdown: finalTab?.aiNoteMarkdown || accumulatedAINote,
                aiAnalyzing: false
              })
              return
            }

            try {
              const parsed = JSON.parse(data)

              if (parsed.type === 'basic_info') {
                // 处理基本信息
                const basicInfo = parsed.data
                useAppStore.getState().updateTab(tabId, {
                  title: basicInfo.title || (type === 'url' ? new URL(input).hostname : '文本内容'),
                  originalContent: basicInfo.content
                })
                basicInfoReceived = true

              } else if (parsed.type === 'ai_note' && basicInfoReceived) {
                // 处理结构化笔记流式更新
                const noteData = parsed.data

                if (noteData.content) {
                  accumulatedAINote += noteData.content

                  // 优化的节流更新：使用requestAnimationFrame和批量处理
                  const now = Date.now()
                  if (now - lastUpdateTime >= UPDATE_THROTTLE || noteData.isComplete) {
                    if (!pendingUpdate) {
                      pendingUpdate = true
                      requestAnimationFrame(() => {
                        useAppStore.getState().setStreamingNote(tabId, accumulatedAINote)
                        lastUpdateTime = Date.now()
                        pendingUpdate = false
                      })
                    }
                  }
                }

                if (noteData.isComplete) {
                  // 完成流式生成，转移到最终状态
                  useAppStore.getState().updateTab(tabId, {
                    aiNoteMarkdown: noteData.fullContent || accumulatedAINote,
                    aiAnalyzing: false
                  })
                  // 清空流式状态
                  useAppStore.getState().clearStreamingNote(tabId)
                }
              }
            } catch (_e) {
              // 忽略解析错误，继续处理下一行
            }
          }
        }
      }
    } catch (error) {
      console.error('流式结构化分析失败:', error)

      // 更详细的错误处理
      let errorMessage = '分析失败'
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = '请求超时，请稍后重试'
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = '网络连接失败，请检查网络'
        } else {
          errorMessage = error.message
        }
      }

      // 更新状态并显示错误信息
      useAppStore.getState().updateTab(tabId, {
        aiAnalyzing: false,
        error: errorMessage
      })
    }
  }

  // 后台处理URL的AI分析（保持向后兼容）
  const processUrlInBackground = async (url: string, tabId: string) => {
    await processContentWithStream(url, 'url', tabId)
  }

  // 直接处理输入的函数
  const handleSubmitDirect = async (input: string) => {
    if (!input.trim()) return

    setProcessing(true)

    try {
      // 判断输入类型并规范化URL
      let processedInput = input.trim()
      let isUrl = false

      // 检测和规范化URL
      if (processedInput.startsWith('http://') || processedInput.startsWith('https://')) {
        isUrl = true
      } else if (processedInput.includes('.') && !processedInput.includes(' ') && processedInput.length > 3) {
        // 可能是没有协议的URL，自动添加https://
        processedInput = 'https://' + processedInput
        isUrl = true
      }

      if (isUrl) {
        // 对于URL：立即创建标签页并显示浏览器，在browse模式下不进行AI分析
        const tabId = addTab({
          title: new URL(processedInput).hostname,
          sourceType: 'url',
          sourceData: processedInput,
          originalContent: '',
          aiNoteMarkdown: '',
          isLoading: false,
          aiAnalyzing: false // browse模式下禁用AI分析
        })

        setInputValue('')
        setProcessing(false)

        // 在browse模式下不调用AI分析，保留接口以备后续使用
        // processUrlInBackground(processedInput, tabId)
      } else {
        // 对于文本：在browse模式下不进行AI分析
        const tabId = addTab({
          title: '文本内容',
          sourceType: 'text',
          sourceData: input,
          originalContent: input, // 直接设置原文内容
          aiNoteMarkdown: '',
          isLoading: false,
          aiAnalyzing: false // browse模式下禁用AI分析
        })

        setInputValue('')
        setProcessing(false)

        // 在browse模式下不调用AI分析，保留接口以备后续使用
        // processContentWithStream(processedInput, 'text', tabId)
      }
    } catch (error) {
      console.error('处理错误:', error)
      setProcessing(false)
      // 可以在这里显示错误提示
    }
  }

  // 处理中栏内部分隔条拖拽
  const handleOriginalSplitterMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDraggingOriginalSplitter(true)

    const startX = e.clientX
    const startWidth = originalPanelWidth

    const handleMouseMove = (e: MouseEvent) => {
      const containerRect = (e.target as HTMLElement).closest('.work-area-container')?.getBoundingClientRect()
      if (!containerRect) return

      const deltaX = e.clientX - startX
      const containerWidth = containerRect.width
      const deltaPercent = (deltaX / containerWidth) * 100

      let newWidth = startWidth + deltaPercent
      
      // 限制拖拽范围：20% - 80%
      newWidth = Math.max(20, Math.min(80, newWidth))
      
      setOriginalPanelWidth(newWidth)
    }

    const handleMouseUp = () => {
      setIsDraggingOriginalSplitter(false)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  // 如果没有标签页，显示新的欢迎界面
  if (tabs.length === 0) {
    return (
      <WelcomeScreen
        onSubmit={(input) => {
          setInputValue(input)
          // 直接调用处理函数，避免事件模拟
          handleSubmitDirect(input)
        }}
        loading={useAppStore.getState().isProcessing}
      />
    )
  }

  // 渲染原文内容
  const renderOriginalContent = () => {
    if (!activeTab) return null

    if (activeTab.sourceType === 'url') {
      return (
        <div className="h-full bg-white">
          <LightBrowser
            url={activeTab.sourceData}
            title={activeTab.title}
            onLoadComplete={() => {}}
            onError={() => {}}
            onNavigateToNewTab={(newUrl) => {
              addTab({
                title: new URL(newUrl).hostname,
                sourceType: 'url',
                sourceData: newUrl,
                originalContent: '',
                aiNoteMarkdown: '',
                isLoading: false,
                aiAnalyzing: true
              })
              const newTabId = useAppStore.getState().tabs[useAppStore.getState().tabs.length - 1]?.id
              if (newTabId) {
                processUrlInBackground(newUrl, newTabId)
              }
            }}
          />
        </div>
      )
    } else {
      return (
        <div className={`h-full flex flex-col ${isPureModeEnabled ? 'bg-white' : 'glass-effect-strong'}`}>
          <div className="flex-1 overflow-y-auto overflow-x-hidden p-6">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-2xl font-bold text-gray-900 mb-6">
                {activeTab.title}
              </h1>
              <div
                className="prose prose-lg max-w-none prose-headings:text-gray-800 prose-headings:font-semibold prose-p:text-gray-700 prose-p:leading-relaxed prose-p:mb-4 prose-ul:text-gray-700 prose-ol:text-gray-700 prose-li:my-1 prose-strong:text-gray-900 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:rounded prose-blockquote:border-l-4 prose-blockquote:border-blue-200 prose-blockquote:pl-4 prose-blockquote:italic prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline"
                style={{
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  lineHeight: '1.7'
                }}
              >
                <SafeMarkdown>
                  {activeTab.originalContent || activeTab.sourceData}
                </SafeMarkdown>
              </div>
            </div>
          </div>
        </div>
      )
    }
  }

  // 渲染结构化笔记内容
  const renderNotesContent = () => {
    if (!activeTab) return null

    const noteContent = streamingNote || activeTab.aiNoteMarkdown
    return (
      <EditableNotesViewer
        content={noteContent}
        loading={activeTab.aiAnalyzing || false}
        title={activeTab.title}
        onContentChange={(_newContent) => {
          // 内容变更会通过store自动同步
        }}
      />
    )
  }

  // 根据当前tab渲染不同内容
  const renderTabContent = () => {
    if (!activeTab || !activeTab.sourceData) {
      return (
        <WelcomeScreen
          onSubmit={(input) => {
            setInputValue(input)
            // 直接调用处理函数，避免事件模拟
            handleSubmitDirect(input)
          }}
          loading={useAppStore.getState().isProcessing}
        />
      )
    }

    if (activeTab.isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">正在处理内容...</p>
          </div>
        </div>
      )
    }

    // 沉淀模式：使用纯净的内容查看器，包含大纲功能
    if (isPureModeEnabled) {
      return <PureContentViewer activeTab={activeTab} />
    }

    // 使用卡片流式布局
    return (
      <CardFlowLayout
        activeTab={activeTab}
        streamingNote={streamingNote}
        onContentChange={(newContent) => {
          // 内容变更会通过store自动同步
        }}
      />
    )
  }

  return (
    <div className="h-full flex flex-col relative">
      <div className="flex-1 overflow-hidden">
        {renderTabContent()}
      </div>
    </div>
  )
}

export default WorkArea
