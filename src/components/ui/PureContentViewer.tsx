'use client'

import React, { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import LightBrowser from './LightBrowser'
import OutlineCard from './OutlineCard'

interface PureContentViewerProps {
  activeTab: {
    id: string
    title: string
    sourceType: 'url' | 'text'
    sourceData: string
    originalContent: string
    aiNoteMarkdown: string
    isLoading: boolean
    aiAnalyzing: boolean
  }
}

const PureContentViewer: React.FC<PureContentViewerProps> = ({ activeTab }) => {
  const [outline, setOutline] = useState<Array<{id: string, title: string, level: number}>>([])
  const contentRef = useRef<HTMLDivElement>(null)

  // 生成内容大纲
  useEffect(() => {
    if (!activeTab.originalContent && !activeTab.sourceData) return

    const content = activeTab.originalContent || activeTab.sourceData
    const lines = content.split('\n')
    const headings: Array<{id: string, title: string, level: number}> = []

    lines.forEach((line, index) => {
      // 检测Markdown标题
      const headingMatch = line.match(/^(#{1,6})\s+(.+)$/)
      if (headingMatch) {
        const level = headingMatch[1].length
        const title = headingMatch[2].trim()
        // 生成更友好的ID
        const id = title.toLowerCase()
          .replace(/[^\w\s-]/g, '') // 移除特殊字符
          .replace(/\s+/g, '-') // 空格替换为连字符
          .substring(0, 50) // 限制长度
        headings.push({ id: id || `heading-${index}`, title, level })
      }
      // 检测其他可能的标题格式
      else if (line.trim() && !line.startsWith(' ') && line.length < 100 && line.length > 3) {
        // 简单的标题检测：短行且不以空格开头
        const nextLine = lines[index + 1]
        if (nextLine && (nextLine.startsWith('=') || nextLine.startsWith('-'))) {
          const title = line.trim()
          const id = title.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .substring(0, 50)
          headings.push({ id: id || `heading-${index}`, title, level: nextLine.startsWith('=') ? 1 : 2 })
        }
      }
    })

    setOutline(headings)
  }, [activeTab.originalContent, activeTab.sourceData])

  // 滚动到指定标题
  const scrollToHeading = (headingId: string) => {
    const element = document.getElementById(headingId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  // 渲染内容
  const renderContent = () => {
    if (activeTab.sourceType === 'url') {
      return (
        <div className="w-full">
          <div className="rounded-2xl overflow-hidden shadow-lg border border-gray-200/50">
            <LightBrowser
              url={activeTab.sourceData}
              title={activeTab.title}
              onLoadComplete={() => {}}
              onError={() => {}}
              className="h-[800px]"
            />
          </div>
        </div>
      )
    } else {
      return (
        <div className="w-full">
          <h1 className="text-3xl font-bold text-gray-900 mb-8 border-b border-gray-200 pb-4">
            {activeTab.title}
          </h1>
          <div 
            ref={contentRef}
            className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-headings:font-semibold prose-p:text-gray-700 prose-p:leading-relaxed prose-p:mb-4 prose-ul:my-4 prose-ol:my-4 prose-li:my-2 prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:my-4 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-pre:bg-gray-900 prose-pre:text-gray-100 prose-pre:p-4 prose-pre:rounded-lg prose-pre:overflow-x-auto"
            style={{ 
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              lineHeight: '1.8'
            }}
          >
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                h1: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h1
                      id={id}
                      className="text-3xl font-bold text-gray-900 mb-6 mt-8 pb-3 border-b-2 border-gray-200"
                      {...props}
                    >
                      {children}
                    </h1>
                  )
                },
                h2: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h2
                      id={id}
                      className="text-2xl font-semibold text-gray-900 mb-4 mt-6 pb-2 border-b border-gray-200"
                      {...props}
                    >
                      {children}
                    </h2>
                  )
                },
                h3: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h3
                      id={id}
                      className="text-xl font-semibold text-gray-900 mb-3 mt-5"
                      {...props}
                    >
                      {children}
                    </h3>
                  )
                },
                h4: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h4
                      id={id}
                      className="text-lg font-medium text-gray-900 mb-2 mt-4"
                      {...props}
                    >
                      {children}
                    </h4>
                  )
                },
                h5: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h5
                      id={id}
                      className="text-base font-medium text-gray-900 mb-2 mt-3"
                      {...props}
                    >
                      {children}
                    </h5>
                  )
                },
                h6: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h6
                      id={id}
                      className="text-sm font-medium text-gray-900 mb-2 mt-3"
                      {...props}
                    >
                      {children}
                    </h6>
                  )
                },
                p: ({ children }) => (
                  <p className="text-gray-700 leading-relaxed mb-4" style={{ whiteSpace: 'pre-wrap' }}>
                    {children}
                  </p>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-inside mb-4 space-y-1 text-gray-700">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-inside mb-4 space-y-1 text-gray-700">
                    {children}
                  </ol>
                ),
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-blue-400 bg-blue-50/30 pl-4 py-2 my-4 rounded-r-lg">
                    {children}
                  </blockquote>
                ),
                code: ({ children, className }) => {
                  const isInline = !className?.includes('language-')
                  if (isInline) {
                    return (
                      <code className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono">
                        {children}
                      </code>
                    )
                  }
                  return (
                    <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto my-4">
                      <code className="text-sm font-mono">{children}</code>
                    </pre>
                  )
                }
              }}
            >
              {activeTab.originalContent || activeTab.sourceData}
            </ReactMarkdown>
          </div>
        </div>
      )
    }
  }

  return (
    <div className="h-full flex relative">
      {/* 左侧大纲卡片 */}
      {outline.length > 0 && (
        <div className="w-80 p-6 flex-shrink-0">
          <OutlineCard
            outline={outline}
            onHeadingClick={scrollToHeading}
          />
        </div>
      )}

      {/* 主内容区域 */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        <div className="max-w-4xl mx-auto px-6 py-8">
          {renderContent()}
        </div>
      </div>
    </div>
  )
}

export default PureContentViewer
